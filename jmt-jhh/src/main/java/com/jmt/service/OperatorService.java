package com.jmt.service;

import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.alibaba.nacos.shaded.com.google.gson.JsonElement;
import com.alibaba.nacos.shaded.com.google.gson.JsonObject;
import com.alibaba.nacos.shaded.com.google.gson.JsonParser;
import com.github.pagehelper.PageHelper;
import com.jmt.client.EqFeignClient;
import com.jmt.client.EqStockFeignClient;
import com.jmt.client.ProfitFeignClient;
import com.jmt.client.UaaFeignClient;
import com.jmt.constant.JmtConstant;
import com.jmt.dao.OperatorDao;
import com.jmt.dao.OperatorInfoAuditDao;
import com.jmt.dao.SalesmanDao;
import com.jmt.exception.BusinessException;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.eq.dto.RequestTrans;
import com.jmt.model.jhh.dto.OperatorAuditDto;
import com.jmt.model.jhh.dto.OperatorInfo;
import com.jmt.model.jhh.dto.OperatorInfoAudit;
import com.jmt.model.jhh.dto.OperatorInfoDto;
import com.jmt.model.jhh.vo.OperatorVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.dto.ShareConfigDto;
import com.jmt.model.uaa.UaaUser;
import com.jmt.model.uaa.dto.UaaUserDTO;
import com.jmt.util.JhhUserNoUtil;
import com.jmt.util.LoginUserUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

@Service
public class OperatorService {
    @Resource
    private OperatorDao operatorDao;
    @Resource
    private OperatorInfoAuditDao operatorInfoAuditDao;
    @Resource
    private SalesmanDao salesmanDao;
    @Resource
    private EqFeignClient eqFeignClient;
    @Resource
    private EqStockFeignClient eqStockFeignClient;
    @Resource
    private UaaFeignClient uaaFeignClient;
    @Resource
    private ProfitFeignClient profitFeignClient;
    /**
     * 添加运营商
     * @param operatorInfoDto
     */
    public void add(OperatorInfoDto operatorInfoDto) {
        OperatorInfo byInvestorNo = operatorDao.getByOperatorNo(operatorInfoDto.getOperatorNo());
        if (operatorInfoDto.getIsProfitLimit()!=null){
            if (operatorInfoDto.getLimitAmount()==null){
                throw new BusinessException("请填写收益限制金额");
            }
            if (operatorInfoDto.getLimitAmount()<=0){
                throw new BusinessException("收益限制金额必须大于0");
            }
            if (operatorInfoDto.getIsProfitLimit()!=1&&operatorInfoDto.getIsProfitLimit()!=0){
                throw new BusinessException("请选择是否限制收益");
            }
        }
        String prefix = "OP";
        String operatorNo = JhhUserNoUtil.generate(prefix);
        OperatorInfo operatorInfo = new OperatorInfo();
        BeanUtils.copyProperties(operatorInfoDto,operatorInfo);
        LocalDateTime now = LocalDateTime.now();
        operatorInfo.setCreateTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        operatorInfo.setIsDelete(0);
        operatorInfo.setAuditStatus(0);
        operatorInfo.setOperatorNo(operatorNo);
        operatorInfo.setIsFirstLogin(1);
        List<ShareConfigDto> shareConfigs = operatorInfoDto.getShareConfigs();
        if (shareConfigs != null && shareConfigs.size() > 0){
            for (ShareConfigDto shareConfigDto : shareConfigs){
                shareConfigDto.setOperatorNo(operatorNo);
                profitFeignClient.add(shareConfigDto);
            }
        }
        operatorDao.add(operatorInfo);
    }

    /**
     * 获取运营商分页列表
     * @param pageQuery
     * @return
     */
    public PageResult<OperatorVo> getPage(PageQuery<OperatorInfoDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<OperatorVo> page = operatorDao.getPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
    /**
     * 获取运营商信息
     * @param id
     * @return
     */
    public OperatorInfo getInfo(Long id) {
        OperatorInfo operatorInfo = operatorDao.getInfo(id);
        return operatorInfo;
    }
    /**
     * 修改运营商信息
     * @param operatorInfoDto
     */
    public void update(OperatorInfoDto operatorInfoDto) {
        OperatorInfo info = operatorDao.getInfo(operatorInfoDto.getId());
        if (operatorInfoDto.getIsProfitLimit()!=null){
            if (operatorInfoDto.getLimitAmount()==null){
                throw new BusinessException("请填写收益限制金额");
            }
            if (operatorInfoDto.getLimitAmount()<=0){
                throw new BusinessException("收益限制金额必须大于0");
            }
            if (operatorInfoDto.getIsProfitLimit()!=1&&operatorInfoDto.getIsProfitLimit()!=0){
                throw new BusinessException("请选择是否限制收益");
            }
        }
        if (info == null){
            throw new BusinessException("该用户不存在");
        }
        OperatorInfo operatorInfo = new OperatorInfo();
        BeanUtils.copyProperties(operatorInfoDto, operatorInfo);
        LocalDateTime now = LocalDateTime.now();
        operatorInfo.setUpdateTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        operatorInfo.setAuditStatus(0);
        operatorInfo.setIsDelete(0);
        operatorInfo.setIsFirstLogin(0);
        operatorDao.update(operatorInfo);
        operatorInfoAuditDao.delete(operatorInfo.getOperatorNo());
        profitFeignClient.deleteByNo(operatorInfoDto.getOperatorNo());
        List<ShareConfigDto> shareConfigs = operatorInfoDto.getShareConfigs();
        if (shareConfigs != null && shareConfigs.size() > 0){
            for (ShareConfigDto shareConfigDto : shareConfigs){
                shareConfigDto.setOperatorNo(operatorInfoDto.getOperatorNo());
                profitFeignClient.add(shareConfigDto);
            }
        }
    }
    /**
     * 删除运营商信息
     * @param id
     */
    public void delete(Long id) {
        OperatorInfo info = operatorDao.getInfo(id);
        if(info.getIsDelete() == 1){
            throw new BusinessException("已删除");
            }
        operatorDao.delete(id);
        operatorInfoAuditDao.delete(info.getOperatorNo());
        profitFeignClient.deleteByNo(info.getOperatorNo());
    }
    /**
     * 运营商信息变更
     * @param operaInfoAuditDto
     * @param request
     */
    public void audit(OperatorAuditDto operaInfoAuditDto, HttpServletRequest request) {
        Integer auditStatus = operaInfoAuditDto.getAuditStatus();
        String reason = operaInfoAuditDto.getReason();
        Long id = operaInfoAuditDto.getId();
        LoginUaaUser loginUaaUser = LoginUserUtil.get(request);
        Long auditUser = loginUaaUser.getUaaId();
        OperatorInfo operatorInfo = operatorDao.getInfo(id);
        if (operatorInfo == null || operatorInfo.getOperatorNo() == null) {
            throw new BusinessException("未找到要审批的对象信息");
        }
        if (auditStatus != 1 && auditStatus != 2) {
            throw new BusinessException("请选择审核状态");
        }
        operatorInfo.setId(id);
        operatorInfo.setAuditStatus(auditStatus);
        LocalDateTime now = LocalDateTime.now();
        operatorInfo.setUpdateTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        operatorInfo.setIsDelete(0);
        operatorInfo.setReason(reason);
        UaaUserDTO uaaUserDTO = new UaaUserDTO();
        uaaUserDTO.setPassword(JmtConstant.DEFAULT_PASSWORD);
        uaaUserDTO.setLoginName(operatorInfo.getTelPhone());
        uaaUserDTO.setTelPhone(operatorInfo.getTelPhone());
        if (auditStatus == 1){
            String response = uaaFeignClient.registerUser(uaaUserDTO);
            Gson gson = new Gson();
            JsonObject jsonObject = JsonParser.parseString(response).getAsJsonObject();
            JsonElement dataElement = jsonObject.get("data");
            UaaUser user = gson.fromJson(dataElement, UaaUser.class);
            Long uaaId = user.getUaaId();
            operatorInfo.setUaaId(uaaId);

        }
        operatorDao.update(operatorInfo);
        OperatorInfoAudit operatorInfoAudit = new OperatorInfoAudit();
        operatorInfoAudit.setAuditStatus(auditStatus);
        operatorInfoAudit.setReason(reason);
        operatorInfoAudit.setOperatorNo(operatorInfo.getOperatorNo());
        operatorInfoAudit.setAuditUser(auditUser);
        operatorInfoAudit.setIsDelete(0);
        operatorInfoAudit.setCreateTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        operatorInfoAudit.setUpdateTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        List<OperatorInfoAudit> operatorInfoAudits = operatorInfoAuditDao.getInfoByOperatorNo(operatorInfo.getOperatorNo());
        if (operatorInfoAudits != null && operatorInfoAudits.size() > 0) {
            for (OperatorInfoAudit operatorInfoAudits1 : operatorInfoAudits){
                if (operatorInfoAudits1.getAuditUser().equals(auditUser)){
                    operatorInfoAuditDao.update(operatorInfoAudit);
                }else{
                    operatorInfoAuditDao.add(operatorInfoAudit);
                }
            }
        }else{
            operatorInfoAuditDao.add(operatorInfoAudit);
        }
    }
    /**
     * 获取运营商下拉列表
     * @return
     */
    public List<OperatorVo> getOperatorSelect() {
        return operatorDao.getOperatorSelect();
    }
    /**
     * 运营商冻结接口
     * @param operaInfoAuditDto
     */
    public void freeze(OperatorAuditDto operaInfoAuditDto, HttpServletRequest request) {
        Integer auditStatus = operaInfoAuditDto.getAuditStatus();
        String reason = operaInfoAuditDto.getReason();
        Long id = operaInfoAuditDto.getId();
        LoginUaaUser loginUaaUser = LoginUserUtil.get(request);
        Long auditUser = loginUaaUser.getUaaId();
        OperatorInfo operatorInfo = operatorDao.getInfo(id);
        if (operatorInfo == null || operatorInfo.getOperatorNo() == null) {
            throw new BusinessException("未找到要审批的对象信息");
        }
        operatorInfo.setId(id);
        operatorInfo.setAuditStatus(3);
        LocalDateTime now = LocalDateTime.now();
        operatorInfo.setUpdateTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        operatorInfo.setIsDelete(0);
        operatorInfo.setReason(reason);
        operatorDao.update(operatorInfo);
        OperatorInfoAudit operatorInfoAudit = new OperatorInfoAudit();
        operatorInfoAudit.setAuditStatus(3);
        operatorInfoAudit.setReason(reason);
        operatorInfoAudit.setOperatorNo(operatorInfo.getOperatorNo());
        operatorInfoAudit.setAuditUser(auditUser);
        operatorInfoAudit.setIsDelete(0);
        List<OperatorInfoAudit> operatorInfoAudits = operatorInfoAuditDao.getInfoByOperatorNo(operatorInfo.getOperatorNo());
        if (operatorInfoAudits != null && operatorInfoAudits.size() > 0) {
            for (OperatorInfoAudit operatorInfoAudits1 : operatorInfoAudits){
                if (operatorInfoAudits1.getAuditUser().equals(auditUser)){
                    operatorInfoAudit.setId(operatorInfoAudits1.getId());
                    operatorInfoAuditDao.update(operatorInfoAudit);
                }else{
                    operatorInfoAuditDao.add(operatorInfoAudit);
                }
            }
        }else{
            operatorInfoAuditDao.add(operatorInfoAudit);
        }
    }
    /**
     * 运营商迁移接口
     *
     * @param
     */
    @Transactional
    public void transfer(RequestTrans requestTrans) {
        String sourceOperatorNo = requestTrans.getSourceOperatorNo();
        String targetOperatorNo = requestTrans.getTargetOperatorNo();
        //校验编号是否有效
        OperatorInfo sourceOperatorInfo = operatorDao.getByOperatorNo(sourceOperatorNo);
        OperatorInfo targetOperatorInfo = operatorDao.getByOperatorNo(targetOperatorNo);
        if (sourceOperatorInfo == null || sourceOperatorInfo.getIsDelete() == 1){
            throw new BusinessException("原运营商编号无效");
        }
        if (targetOperatorInfo == null || targetOperatorInfo.getIsDelete() == 1){
            throw new BusinessException("目标运营商编号无效");
        }
        if (targetOperatorInfo.getAuditStatus()!=1){
            throw new BusinessException("目标运营商未审核通过");
        }
        if (sourceOperatorInfo.getAuditStatus()!=1){
            throw new BusinessException("原运营商未审核通过");
        }
        // 迁移设备（更新设备的运营商编号）
        eqFeignClient.transfer(requestTrans);
        // 迁移业务员（更新业务员所属运营商编号）
        salesmanDao.transfer(sourceOperatorNo,targetOperatorNo);
        //迁移业务员设备（更新业务员设备）
        // 合并库存（将原运营商库存累加到目标运营商）
        eqStockFeignClient.transferStock(requestTrans);
        // 删除原运营商设备
        eqStockFeignClient.clearOperatorEq(sourceOperatorNo);
//        try {
//
//        }catch (Exception e){
//            throw new BusinessException("调用eq-eq接口异常");
//        }

    }
    /**
     * 根据uaaId获取运营商信息
     * @param uaaId
     * @return
     */
    public OperatorInfo getOperatorInfoByUaaId(Long uaaId) {
        return operatorDao.getByUaaId(uaaId);
    }

    public OperatorInfo getOperatorInfoByOperatorNo(String operatorNo) {
        return operatorDao.getByOperatorNo(operatorNo);
    }
}
