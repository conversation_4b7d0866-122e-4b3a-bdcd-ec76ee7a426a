package com.jmt.lkl;

import com.jmt.model.profit.dto.LklRefundDTO;
import com.lkl.laop.sdk.LKLSDK;
import com.lkl.laop.sdk.request.V3LabsRelationRefundRequest;
import com.lkl.laop.sdk.request.model.V3LabsTradeLocationInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class LakalaRefundService extends LakalaBaseCommon {

    // 初始化配置
    static {
        try {
            doInit();
        } catch (Exception e) {
            log.error("拉卡拉支付初始化失败，请检查配置或证书！", e);
            throw new RuntimeException("支付系统初始化失败", e);
        }
    }

    /**
     * 创建退款请求
     * @param dto
     * @return
     */
    public V3LabsRelationRefundRequest createRefundRequest(LklRefundDTO dto) {
        return createRefundRequest(
                dto.getMerchantNo(),
                dto.getTermNo(),
                dto.getOutTradeNo(),
                dto.getRefundAmount(),
                dto.getRefundReason(),
                dto.getOriginOutTradeNo(),
                dto.getOriginLogNo(),
                dto.getOriginTradeNo(),
                dto.getIpAddress(),
                dto.getGpsLocation()
        );
    }

    /**
     * 创建拉卡拉退款请求
     * @param merchantNo 商户号
     * @param termNo 终端号
     * @param outTradeNo 本次退款单号(商户系统生成)
     * @param refundAmount 退款金额(单位:分)
     * @param refundReason 退款原因
     * @param originOutTradeNo 原支付订单的商户订单号
     * @param originLogNo 原支付订单的拉卡拉日志号
     * @param originTradeNo 原支付订单的拉卡拉交易号
     * @param ipAddress 客户端IP地址
     * @param gpsLocation GPS位置信息(格式: "经度,纬度")
     * @return 退款请求对象
     */
    public V3LabsRelationRefundRequest createRefundRequest(
            String merchantNo, String termNo, String outTradeNo,
            String refundAmount, String refundReason, String originOutTradeNo,
            String originLogNo, String originTradeNo, String ipAddress,
            String gpsLocation) {

        // 参数校验
        if (StringUtils.isEmpty(merchantNo) || StringUtils.isEmpty(termNo)
                || StringUtils.isEmpty(outTradeNo) || StringUtils.isEmpty(refundAmount)
                || StringUtils.isEmpty(originOutTradeNo) || StringUtils.isEmpty(originTradeNo)) {
            throw new IllegalArgumentException("必要参数不能为空");
        }

        V3LabsRelationRefundRequest request = new V3LabsRelationRefundRequest();
        request.setMerchantNo(merchantNo);
        request.setTermNo(termNo);
        request.setOutTradeNo(outTradeNo);
        request.setRefundAmount(refundAmount);
        request.setRefundReason(refundReason);
        request.setOriginOutTradeNo(originOutTradeNo);
        request.setOriginLogNo(originLogNo);
        request.setOriginTradeNo(originTradeNo);

        // 设置位置信息
        V3LabsTradeLocationInfo locationInfo = new V3LabsTradeLocationInfo(
                ipAddress,
                null, // 基站信息(可选)
                gpsLocation
        );
        request.setLocationInfo(locationInfo);

        return request;
    }

    /**
     * 执行退款请求
     * @param refundRequest 退款请求对象
     * @return 退款响应结果(JSON字符串)
     * @throws Exception
     */
    public static String executeRefund(V3LabsRelationRefundRequest refundRequest) throws Exception {
        return LKLSDK.httpPost(refundRequest);
    }
}
