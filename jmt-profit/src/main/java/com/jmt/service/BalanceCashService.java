package com.jmt.service;
import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jmt.base.BaseService;
import com.jmt.client.UaaFeignClient;
import com.jmt.dao.*;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.BusinessException;
import com.jmt.model.profit.dto.*;
import com.jmt.model.profit.entity.ProfitBalanceRecord;
import com.jmt.model.profit.entity.ProfitPayRecord;
import com.jmt.model.profit.vo.BalanceRechargeOrderVo;
import com.jmt.model.uaa.UaaUserBankcard;
import com.jmt.model.uaa.UaaUserWallet;
import com.jmt.util.DateUtil;
import com.jmt.util.ProfitUserNoUtil;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class BalanceCashService extends BaseService {
    //最低提现余额
    private static final BigDecimal MIN_BALANCE = new BigDecimal("10");
    //最高提现余额
    private static final BigDecimal MAX_BALANCE = new BigDecimal("50000");
    //需财务审核的金额下限
    private static final BigDecimal MIN_AUDIT_BALANCE = new BigDecimal("1000");
    //每日提现上限
    private static final int MAX_DAY_COUNT = 5;
    //每周提现上限
    private static final int MAX_WEEK_COUNT = 10;
    //每月提现上限
    private static final int MAX_MONTH_COUNT = 20;
    //最小充值金额
    private static final BigDecimal MIN_RECHARGE_AMOUNT = new BigDecimal("10");
    @Resource
    private UaaFeignClient uaaFeignClient;
    @Resource
    private BalanceRecordDao balanceRecordDao;
    @Resource
    private BalanceApplyDao balanceApplyDao;
    @Resource
    private BalanceApplyAuditDao balanceApplyAuditDao;
    @Resource
    private BalanceRechargeOrderDao balanceRechargeOrderDao;
    @Resource
    private ProfitPayRecordDao profitPayRecordDao;
    /**
     * 申请兑现
     *
     * @param balanceApplyDto
     * @param applyUser
     */
    @Transactional(rollbackFor = Exception.class)
    public void apply(BalanceApplyDto balanceApplyDto, Long applyUser) {
        // 1. 校验用户存在
        BalanceApply balanceApply = new BalanceApply();
        BeanUtils.copyProperties(balanceApplyDto, balanceApply);
        String byUaaId = uaaFeignClient.getByUaaId(applyUser);
        Gson gson = new Gson();
        JsonObject jsonObject = JsonParser.parseString(byUaaId).getAsJsonObject();
        JsonElement dataElement = jsonObject.get("data");
        UaaUserWallet userWallet = gson.fromJson(dataElement, UaaUserWallet.class);
        // 2. 获取余额配置，校验积分是否满足兑现条件
        BigDecimal balance = balanceApplyDto.getBalance();
        validatePointsCashCondition(userWallet,balance);
        // 3. 校验提现频次限制（每天/每周/每月）
        Date now = new Date();
        Date startDay = DateUtil.getDayStart(now);
        Date endDay = DateUtil.getDayEnd(now);
        Date startWeek = DateUtil.getWeekStart(now);
        Date endWeek = DateUtil.getWeekEnd(now);
        Date startMonth = DateUtil.getMonthStart(now);
        Date endMonth = DateUtil.getMonthEnd(now);
        int countDay = balanceApplyDao.getCountByUaaIdAndCreateTime(applyUser, startDay, endDay);
        int countWeek = balanceApplyDao.getCountByUaaIdAndCreateTime(applyUser, startWeek, endWeek);
        int countMonth = balanceApplyDao.getCountByUaaIdAndCreateTime(applyUser, startMonth, endMonth);
        if (countDay >= MAX_DAY_COUNT){
            throw new RuntimeException("今日已超过最大提现次数限制");
        }
        if (countWeek >= MAX_WEEK_COUNT){
            throw new RuntimeException("本周已超过最大提现次数限制");
        }
        if (countMonth >= MAX_MONTH_COUNT){
            throw new RuntimeException("本月已超过最大提现次数限制");
        }
        //查看银行卡是否存在
        Long bankId = balanceApplyDto.getBankId();
        String byId = uaaFeignClient.getById(bankId);
        Gson gsonBank = new Gson();
        JsonObject jsonObjectBank = JsonParser.parseString(byId).getAsJsonObject();
        JsonElement dataElementBank = jsonObjectBank.get("data");
        UaaUserBankcard userBankcard = gsonBank.fromJson(dataElementBank, UaaUserBankcard.class);
        if (userBankcard == null){
            throw new BusinessException("银行卡不存在");
        }
        try {
            // 4. 扣减用户余额
            uaaFeignClient.decreaseBalance(applyUser, balance);
            // 5.根据是否达到金额下限决定是否需要审核
            if (balance.compareTo(MIN_AUDIT_BALANCE) > 0) {
                balanceApply.setAuditStatus("0");// 待审核
            }else{
                balanceApply.setAuditStatus("1");// 无需审核
                //调用第三方汇款接口
            }
            //记录余额变动
            BalanceRecord balanceRecord = new BalanceRecord();
            balanceRecord.setUaaId(applyUser);
            balanceRecord.setBalance(balance);
            balanceRecord.setRecordType(1);
            balanceRecord.setCreateTime(now);
            balanceRecord.setUpdateTime(now);
            balanceRecord.setRemark(balanceApplyDto.getReason());
            balanceRecord.setIsDelete(0);
            balanceRecordDao.add(balanceRecord);
            // 6.创建余额提现申请表
            balanceApply.setCreateTime(now);
            balanceApply.setUpdateTime(now);
            balanceApply.setIsDelete(0);
            String applyNo = ProfitUserNoUtil.generate("BA");
            balanceApply.setApplyNo(applyNo);
            balanceApply.setApplyUser(applyUser);
            balanceApplyDao.add(balanceApply);
        }catch (Exception e){
            // 扣减失败，回滚提现申请,退还余额,并更改状态
            uaaFeignClient.increaseBalance(balanceApplyDto.getApplyUser(), balanceApplyDto.getBalance());
            balanceApply.setAuditStatus("3");
            balanceApplyDao.add(balanceApply);
            throw e;
        }
    }
    /**
     * 校验积分兑现条件
     */
    private void validatePointsCashCondition(UaaUserWallet userWallet, BigDecimal balance) {
        // 校验最低提现余额
        if (balance.compareTo(MIN_BALANCE)<0){
            throw new BusinessException("积分低于最低提现限制");
        }
        // 校验用户余额是否足够
        if (userWallet.getBalance().compareTo(balance)<0) {
            throw new BusinessException("用户余额不足");
        }
        // 校验最高兑现余额
        if (balance.compareTo(MAX_BALANCE)>0) {
            throw new BusinessException("积分高于最高提现限制");
        }
    }
    /**
     * 获取用户余额提现申请列表
     * @param pageQuery
     * @return
     */
    public PageResult<BalanceApply> getPage(PageQuery<BalanceApplyDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<BalanceApply> page = balanceApplyDao.getPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
    @Transactional(rollbackFor = Exception.class)
    public void audit(BalanceApplyAuditDto balanceApplyAuditDto, Long auditUser) {
        Long id = balanceApplyAuditDto.getId();
        Date now = new Date();
        String auditStatus = balanceApplyAuditDto.getAuditStatus();
        String reason = balanceApplyAuditDto.getReason();
        //查询余额兑现申请
        BalanceApply balanceApply = balanceApplyDao.getInfo(id);
        Long applyUser = balanceApply.getApplyUser();
        String byUaaId = uaaFeignClient.getByUaaId(applyUser);
        Gson gson = new Gson();
        JsonObject jsonObject = JsonParser.parseString(byUaaId).getAsJsonObject();
        JsonElement dataElement = jsonObject.get("data");
        UaaUserWallet userWallet = gson.fromJson(dataElement, UaaUserWallet.class);
        if (balanceApply == null){
            throw new BusinessException("申请不存在");
        }
        //校验申请状态
        if (!balanceApply.getAuditStatus().equals("0")){
            throw new BusinessException("申请已处理");
        }
        //校验审核状态
        if (!(auditStatus.equals("1")||auditStatus.equals("2"))){
            throw new BusinessException("审核状态有误");
        }
        try {
            //更新申请表申请状态
            balanceApply.setUpdateTime(now);
            balanceApply.setReason(reason);
            balanceApply.setAuditStatus(auditStatus);
            balanceApplyDao.update(balanceApply);
            //更新审核表申请状态
            BalanceCashApplyAudit applyAudit = new BalanceCashApplyAudit();
            applyAudit.setAuditUser(auditUser);
            applyAudit.setApplyNo(balanceApply.getApplyNo());
            applyAudit.setReason(reason);
            applyAudit.setIsDelete(0);
            applyAudit.setUpdateTime(now);
            applyAudit.setAuditStatus(auditStatus);
            updateAudits(applyAudit,balanceApply, auditUser);
            //计算兑现金额(审核通过)
            if (auditStatus.equals("1")){
                //审核通过，开始打款
                applyAudit.setAuditStatus("3");
                balanceApply.setAuditStatus("3");
                balanceApplyDao.update(balanceApply);
                updateAudits(applyAudit,balanceApply,applyUser);
            } else{
                // 4.2 审核拒绝，退还余额
//            userWallet.setBalance(userWallet.getBalance().add(balanceApply.getBalance()));
                uaaFeignClient.increaseBalance(applyUser, balanceApply.getBalance());
                // 记录余额变动
                BalanceRecord balanceRecord = new BalanceRecord();
                balanceRecord.setUaaId(applyUser);
                balanceRecord.setBalance(balanceApply.getBalance());
                balanceRecord.setRecordType(0);
                balanceRecord.setBalanceSource(1);
                balanceRecord.setSourceId(balanceApply.getApplyNo());
                balanceRecord.setRemark(reason);
                balanceRecord.setCreateTime(now);
                balanceRecord.setIsDelete(0);
                balanceRecord.setUpdateTime(now);
                balanceRecordDao.add(balanceRecord);
            }
        }catch (Exception e){
            // 审核失败,回滚提现申请,退还余额,并更改状态
            uaaFeignClient.increaseBalance(applyUser, balanceApply.getBalance());
            balanceApply.setAuditStatus("3");
            throw e;
        }


    }
    /**
     * 创建订单
     *
     * @param PayAmount
     * @return
     */
    public BalanceRechargeOrder createOrder(BigDecimal PayAmount, Long rechargeUser) {
        // 1. 校验充值金额
        if (PayAmount.compareTo(MIN_RECHARGE_AMOUNT) <= 0){
            throw new BusinessException( "充值金额不能小于" + MIN_RECHARGE_AMOUNT);
        }
        // 2. 创建充值订单
        Date now = new Date();
        BalanceRechargeOrder balanceRechargeOrder = new BalanceRechargeOrder();
        balanceRechargeOrder.setOrderNo(ProfitUserNoUtil.generate("BR"));
        balanceRechargeOrder.setCreateTime(now);
        balanceRechargeOrder.setUpdateTime(now);
        balanceRechargeOrder.setIsDelete(0);
        balanceRechargeOrder.setRechargeUser(rechargeUser);
        balanceRechargeOrder.setPayAmount(PayAmount);
        balanceRechargeOrder.setOrderStatus(0);
        balanceRechargeOrder.setOrderName("余额充值");
        balanceRechargeOrderDao.add(balanceRechargeOrder);
        return balanceRechargeOrder;
    }
    private  boolean pay(BalanceRechargeOrder balanceRechargeOrder) {
        // 模拟支付
        // 示例返回成功
        return true;
    }

    /**
     * 获取余额充值订单列表
     * @param pageQuery
     * @return
     */
    public PageResult<BalanceRechargeOrderVo> getBalancePage(PageQuery<BalanceRechargeOrderDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<BalanceRechargeOrderVo> page = balanceRechargeOrderDao.getBalancePage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }

    public void handlePayCallback(String orderNo,Long rechargeUser) {
        BalanceRechargeOrder balanceRechargeOrder = balanceRechargeOrderDao.getInfoByOrderNo(orderNo);
        Date now = new Date();
        if (balanceRechargeOrder == null || balanceRechargeOrder.getOrderStatus() != 0) { // 非待支付状态
            throw new BusinessException("订单不存在或已支付");
        }
        ProfitPayRecord profitPayRecord = profitPayRecordDao.selectByOrderNo(orderNo);
        if (profitPayRecord.getPayStatus() == 1){
            try {
                //更改订单状态
                balanceRechargeOrder.setOrderStatus(1);
                balanceRechargeOrder.setId(balanceRechargeOrder.getId());
                balanceRechargeOrderDao.update(balanceRechargeOrder);
                // 增加余额
                uaaFeignClient.increaseBalance(rechargeUser, balanceRechargeOrder.getPayAmount());
                //记录余额变动
                BalanceRecord balanceRecord = new BalanceRecord();
                balanceRecord.setUaaId(rechargeUser);
                balanceRecord.setBalance(balanceRechargeOrder.getPayAmount());
                balanceRecord.setRecordType(0);
                balanceRecord.setBalanceSource(1);
                balanceRecord.setSourceId(balanceRechargeOrder.getOrderNo());
                balanceRecord.setRemark(balanceRechargeOrder.getReason());
                balanceRecord.setCreateTime(now);
                balanceRecord.setIsDelete(0);
                balanceRecord.setUpdateTime(now);
                balanceRecordDao.add(balanceRecord);
            }catch (Exception e){
                // 余额增加失败，回滚订单状态
                balanceRechargeOrder.setOrderStatus(3); // 余额处理失败
                balanceRechargeOrderDao.update(balanceRechargeOrder);
                throw new BusinessException("余额处理失败");
            }

        }else{
            balanceRechargeOrder.setOrderStatus(2);
            balanceRechargeOrderDao.update(balanceRechargeOrder);
        }
    }
    private void updateAudits(BalanceCashApplyAudit applyAudit,BalanceApply balanceApply, Long auditUser) {
        List<BalanceCashApplyAudit> applyAudits = balanceApplyAuditDao.getInfoByApplyNo(balanceApply.getApplyNo());
        if (applyAudits != null && applyAudits.size() > 0) {
            log.info("有多个相同编号的审核信息");
            for (BalanceCashApplyAudit applyAudit1 : applyAudits){
                if (applyAudit1.getAuditUser().equals(auditUser)){
                    log.info("有i相同uuid的记录");
                    applyAudit.setId(applyAudit1.getId());
                    balanceApplyAuditDao.update(applyAudit);
                }else {
                    log.info("无i相同uuid的记录");
                    applyAudit.setCreateTime(new Date());
                    balanceApplyAuditDao.add(applyAudit);
                }
            }
        }else{
            log.info("首次审核");
            applyAudit.setCreateTime(new Date());
            balanceApplyAuditDao.add(applyAudit);
        }
    }
}
